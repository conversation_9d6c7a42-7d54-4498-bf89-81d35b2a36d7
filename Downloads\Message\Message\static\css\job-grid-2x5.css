/* 2x5 Job Grid Layout - Dedicated CSS File */

/* Main Grid Container - 2x5 Layout (2 rows, 5 columns) - CUSTOM SIZE */
.jobs-list,
#jobsList {
    display: grid !important;
    grid-template-columns: repeat(5, 700px) !important;
    grid-template-rows: repeat(2, 500px) !important;
    gap: 40px !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    align-items: stretch !important;
    justify-items: stretch !important;
    box-sizing: border-box !important;
    min-height: 1080px !important;
}

/* Job Card Base Styling for Grid - CUSTOM SIZE */
.modern-job-card {
    background-color: #fff !important;
    border-radius: 20px !important;
    border: 2px solid #e5e7eb !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
    width: 700px !important;
    min-width: 700px !important;
    max-width: 700px !important;
    height: 500px !important;
    min-height: 500px !important;
    max-height: 500px !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
    box-sizing: border-box !important;
}

.modern-job-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-3px) !important;
    border-color: #CD208B !important;
}

/* Card Header - MEGA SIZE */
.modern-job-card .card-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    padding: 25px 30px 18px !important;
    background-color: #fff !important;
    border-bottom: 2px solid #f1f3f4 !important;
    min-height: 90px !important;
}

.modern-job-card .job-title-main {
    font-size: 1.5rem !important;
    font-weight: 800 !important;
    color: #1f2937 !important;
    margin: 0 !important;
    line-height: 1.4 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

.modern-job-card .posted-date {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
}

/* Card Body - MEGA SIZE */
.modern-job-card .card-body {
    padding: 0 30px 25px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    background-color: #fff !important;
    overflow: hidden !important;
}

/* Client Profile Section - Compact */
.modern-job-card .client-profile-section {
    display: flex !important;
    align-items: flex-start !important;
    gap: 10px !important;
    margin-bottom: 12px !important;
    padding: 10px !important;
    background-color: #f8fafc !important;
    border-radius: 8px !important;
    border-left: 3px solid #cd208b !important;
    min-height: 60px !important;
}

.modern-job-card .client-avatar-container {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    border: 2px solid #e5e7eb !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
}

.modern-job-card .client-avatar-img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

.modern-job-card .client-details {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
    min-width: 0 !important;
}

.modern-job-card .client-business-name {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin: 0 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.modern-job-card .client-rating {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    font-size: 0.75rem !important;
}

.modern-job-card .client-specialty {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
    font-style: italic !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* Job Description - Compact */
.modern-job-card .job-description-section {
    margin-bottom: 12px !important;
    padding: 10px !important;
    background-color: #f9fafb !important;
    border-radius: 6px !important;
    border-left: 2px solid #e5e7eb !important;
    font-size: 0.8rem !important;
    color: #4b5563 !important;
    line-height: 1.4 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
}

/* Job Details Grid - Compact */
.modern-job-card .job-details-grid {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 6px !important;
    margin-bottom: 12px !important;
}

.modern-job-card .detail-item {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    padding: 6px 8px !important;
    background-color: #f8fafc !important;
    border-radius: 6px !important;
    border: 1px solid #e5e7eb !important;
    font-size: 0.75rem !important;
    color: #374151 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.modern-job-card .detail-item i {
    color: #004aad !important;
    width: 12px !important;
    text-align: center !important;
    flex-shrink: 0 !important;
    font-size: 0.7rem !important;
}

/* Card Footer - Compact */
.modern-job-card .card-footer {
    padding: 8px 14px !important;
    border-top: 1px solid #f1f3f4 !important;
    background-color: #fff !important;
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
    margin-top: auto !important;
}

.modern-job-card .view-details-btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 6px 12px !important;
    background-color: #cd208b !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
    font-size: 0.75rem !important;
    transition: all 0.2s ease !important;
}

.modern-job-card .view-details-btn:hover {
    background-color: #b91c7a !important;
    color: white !important;
    text-decoration: none !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(205, 32, 139, 0.3) !important;
}

/* Responsive Breakpoints for 2x5 Grid */
@media (max-width: 1600px) {
    .jobs-list, #jobsList {
        grid-template-columns: repeat(4, 1fr) !important;
        grid-template-rows: auto !important;
        gap: 12px !important;
    }
}

@media (max-width: 1300px) {
    .jobs-list, #jobsList {
        grid-template-columns: repeat(3, 1fr) !important;
        grid-template-rows: auto !important;
        gap: 12px !important;
    }
}

@media (max-width: 1000px) {
    .jobs-list, #jobsList {
        grid-template-columns: repeat(2, 1fr) !important;
        grid-template-rows: auto !important;
        gap: 10px !important;
    }
}

@media (max-width: 600px) {
    .jobs-list, #jobsList {
        grid-template-columns: 1fr !important;
        grid-template-rows: auto !important;
        gap: 8px !important;
    }
    
    .modern-job-card {
        min-height: 300px !important;
    }
}

/* Override any conflicting styles - CUSTOM CONTAINER */
.jobs-container {
    width: 100% !important;
    max-width: 3800px !important;
    margin: 0 auto !important;
    padding: 0 50px !important;
}

/* Force grid display over any other layout */
.jobs-list {
    display: grid !important;
    grid-auto-flow: row !important;
}

/* Ensure cards fill the grid properly */
.modern-job-card {
    grid-column: span 1 !important;
    grid-row: span 1 !important;
}
